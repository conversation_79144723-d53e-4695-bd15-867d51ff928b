@import '~assets/sass/variables';

.loading-table {
    &__row {
        box-shadow: 0 1px 0 0 rgba($gray, 0.3);
    }

    td {
        padding-right: 16px;
        padding-left: 16px;
    }

    td:first-of-type {
        padding-left: 24px;
    }

    td:last-of-type {
        padding-right: 24px;
    }

    &__loader {
        height: 10px;
        width: 65%;
        background: linear-gradient(90deg, #e3e3e3, #f5f5f5);
        border-radius: 2px;
        background-size: 600% 600%;
        animation: gradientAnimation 3s ease;
        animation-iteration-count: infinite;
    }

    .mat-column-select,
    .mat-column-report,
    .mat-column-download,
    .mat-column-open,
    .mat-column-showMore,
    .mat-column-actions {
        .loading-table__loader {
            position: relative;
            width: 88px;
            height: 32px;

            &:before {
                content: '';
                position: absolute;
                top: 13px;
                left: 50%;
                display: block;
                width: 42px;
                height: 6px;
                transform: translateX(-50%);
                background: rgba(255, 255, 255, 0.7);
            }
        }
    }

    .mat-column-report,
    .mat-column-download {
        .loading-table__loader {
            width: 104px;
        }
    }

    .mat-column-open {
        padding-left: 0;
    }
}

@keyframes gradientAnimation {
    0% {
        background-position: 0 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}
