<div class="sam-history">
    <table
        mat-table
        [dataSource]="samHistory$ | async"
        [trackBy]="trackByFn"
        class="sam-history-table__table"
    >
        <!-- Name Column -->
        <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Name</th>
            <td mat-cell *matCellDef="let item">{{ item.name }}</td>
        </ng-container>

        <!-- Type Column -->
        <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef>Type</th>
            <td mat-cell *matCellDef="let item">{{ item.type }}</td>
        </ng-container>

        <!-- Results Column -->
        <ng-container matColumnDef="results">
            <th mat-header-cell *matHeaderCellDef>Results</th>
            <td mat-cell *matCellDef="let item">{{ item.results }}</td>
        </ng-container>

        <!-- Last Updated Column -->
        <ng-container matColumnDef="lastUpdatedAt">
            <th mat-header-cell *matHeaderCellDef>Last Updated</th>
            <td mat-cell *matCellDef="let item">{{ item.lastUpdatedAt | date:'d MMM y, HH:mm' }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <!-- Loading placeholder -->
    <avl-table-loading-placeholder
        *ngIf="isLoading$ | async"
        [columns]="displayedColumns"
        [size]="3"
    ></avl-table-loading-placeholder>

    <!-- No data message -->
    <avl-table-no-data-disclaimer
        *ngIf="!(isLoading$ | async) && (samHistory$ | async)?.length === 0"
        message="No SAM history available"
    ></avl-table-no-data-disclaimer>
</div>
