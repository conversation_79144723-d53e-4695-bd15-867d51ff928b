import { Injectable } from '@angular/core';
import { BaseSelection } from './base-selection';
import { SelectionStrategy } from '../types/selection-strategy.type';
import { Observable, Subject } from 'rxjs';
import { BoundsEndSelectionEventData } from '../types/bounds-end-selection-event-data.type';
import { LngLat, Map } from 'maplibre-gl';
import { debounceTime, filter, takeUntil } from 'rxjs/operators';
import { SelectTool } from '../enums/select-tool.enum';
import { Feature, Polygon } from 'geojson';
import { bboxPolygon, booleanPointInPolygon, point } from '@turf/turf';
import { LoggerService } from '@services';
import { MapBounds } from '../../map-search/types/mapping-bounds.type';

@Injectable()
export class BoundsSelectionStrategyService extends BaseSelection implements SelectionStrategy<BoundsEndSelectionEventData> {
    private readonly confirmSelectionEvent$ = new Subject<BoundsEndSelectionEventData>();
    private readonly msDebounce = 1000;
    private readonly select$ = new Subject<void>();
    private destroy$ = new Subject<void>();
    private selectedBounds: MapBounds | null = null;

    constructor(
        private readonly log: LoggerService,
    ) {
        super();
        this.select = this.select.bind(this);
    }

    public isPointInsideShape(pointCoordinates: LngLat): boolean {
        const loadedAreaPolygon = this.getLoadedAreaPolygon();
        const targetPoint = point([pointCoordinates.lng, pointCoordinates.lat]);

        return loadedAreaPolygon
            ? booleanPointInPolygon(targetPoint, loadedAreaPolygon)
            : false;
    }

    public activate(map: Map): void {
        super.activate(map);
        this.destroy$ = new Subject<void>();
        this.listenSelect();
        this.subscribeOnMapEvents();
    }

    public deactivate(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.unsubscribeFromMapEvents();
        super.deactivate();
    }

    public startSelecting(): void {
        super.startSelecting();
        this.select();
    }

    public select(): void {
        this.select$.next();
    }

    public confirmSelection(): void {
        const bounds = this.getBounds();
        if (bounds) {
            this.selectedBounds = bounds;
            this.confirmSelectionEvent$.next(bounds);
        }
    }

    public confirmSelectionEvent(): Observable<BoundsEndSelectionEventData> {
        return this.confirmSelectionEvent$.asObservable();
    }

    public getLoadedAreaPolygon(): Feature<Polygon> | null {
        try {
            if (!this.selectedBounds) {
                this.log.warn('Cannot create polygon: no selected bounds');
                return null;
            }

            const { lng: swLng, lat: swLat } = this.selectedBounds.swPoint;
            const { lng: neLng, lat: neLat } = this.selectedBounds.nePoint;
            const bboxCoords: [number, number, number, number] = [swLng, swLat, neLng, neLat];

            return bboxPolygon(bboxCoords);
        } catch (error) {
            this.log.error('Error creating bounding box polygon:', error);
            return null;
        }
    }

    public clear(): void {
        // No implementation
    }

    public undo(): void {
        // No implementation
    }

    public setStyle(): void {
        // No implementation
    }

    protected render(): void {
        // No implementation
    }

    private getBounds(): BoundsEndSelectionEventData {
        const bounds = this.map.getBounds();
        const swPoint = new LngLat(bounds.getSouthWest().lng, bounds.getSouthWest().lat);
        const nePoint = new LngLat(bounds.getNorthEast().lng, bounds.getNorthEast().lat);

        return {
            type: SelectTool.bounds,
            swPoint,
            nePoint,
        };
    }

    private listenSelect(): void {
        this.select$
            .pipe(
                takeUntil(this.destroy$),
                filter(() => this.isSelection),
                debounceTime(this.msDebounce),
                filter(() => this.isSelection),
            )
            .subscribe(() => {
                this.confirmSelection();
            });
    }

    private subscribeOnMapEvents(): void {
        this.map.on('zoomend', this.select);
        this.map.on('moveend', this.select);
        this.map.on('resize', this.select);
    }

    private unsubscribeFromMapEvents(): void {
        this.map.off('zoomend', this.select);
        this.map.off('moveend', this.select);
        this.map.off('resize', this.select);
    }

}
