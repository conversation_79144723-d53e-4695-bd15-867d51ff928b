import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { SamHistory } from '../types/sam-history.type';
import { samHistoryMock } from '../../../../mocks/sam-history.mock';

@Injectable()
export class SamHistoryApi {

    constructor(
        private readonly http: HttpClient,
    ) {
    }

    public getAll(): Observable<HttpResponse<SamHistory[]>> {
        return of(
            new HttpResponse<SamHistory[]>({
                body: samHistoryMock,
                status: 200,
                statusText: 'OK',
                url: 'api/mapping/history',
            })
        );
        // return this.http.get<SamHistory[]>('api/mapping/history', { observe: 'response' });
    }

    public delete(id: string): Observable<void> {
        return this.http.delete<void>(`api/mapping/history/${id}`);
    }
}
