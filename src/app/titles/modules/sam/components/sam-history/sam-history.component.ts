import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { SamHistoryService } from '../../stores/sam-history/sam-history.service';
import { SamHistoryQuery } from '../../stores/sam-history/sam-history.query';
import { SamHistory } from '../../types/sam-history.type';

@Component({
    selector: 'avl-sam-history',
    templateUrl: './sam-history.component.html',
    styleUrls: ['./sam-history.component.scss'],
})
export class SamHistoryComponent implements OnInit, OnDestroy {
    public samHistory$: Observable<SamHistory[]>;
    public isLoading$: Observable<boolean>;
    public displayedColumns: string[] = ['name', 'type', 'results', 'lastUpdatedAt'];

    constructor(
        private readonly samHistoryService: SamHistoryService,
        private readonly samHistoryQuery: SamHistoryQuery,
    ) {
        this.samHistory$ = this.samHistoryQuery.selectAll();
        this.isLoading$ = this.samHistoryQuery.selectLoading();
    }

    public ngOnInit(): void {
        this.samHistoryService.init();
        this.samHistoryService.loadHistory();
    }

    public ngOnDestroy(): void {
        this.samHistoryService.clear();
        this.samHistoryService.finalize();
    }

    public trackByFn(index: number, item: SamHistory): string {
        return item.id;
    }

}
