import { NgModule } from '@angular/core';
import { SplashScreenComponent } from './components/splash-screen/splash-screen.component';
import { OnboardingOverlayComponent } from './components/onboarding-overlay/onboarding-overlay.component';
import { SharedModule } from '@shared/shared.module';
import { CharacterViewComponent } from './components/character-view/character-view.component';
import { MedalComponent } from './components/medal/medal.component';
import { InitiateOnboardingResolver } from './resolvers/initiate-onboarding.resolver';
import { ConfirmExitOnboardingGuard } from './guards/confirm-exit-onboarding.guard';
import { LottieModule } from 'ngx-lottie';

@NgModule({
    declarations: [
        OnboardingOverlayComponent,
        SplashScreenComponent,
        CharacterViewComponent,
        MedalComponent,
    ],
    providers: [
        InitiateOnboardingResolver,
        ConfirmExitOnboardingGuard,
    ],
    imports: [
        SharedModule,
        LottieModule,
    ],
})
export class OnboardingModule {
}
