import { Injectable } from '@angular/core';
import { SelectionStrategy } from '../types/selection-strategy.type';
import { CircleSelectionStrategyService } from './circle-selection-strategy.service';
import { PolygonSelectionStrategyService } from './polygon-selection-strategy.service';
import { LngLat, Map } from 'maplibre-gl';
import { CircleEndSelectionEventData } from '../types/circle-end-selection-event-data.type';
import { PolygonEndSelectionEventData } from '../types/polygon-end-selection-event-data.type';
import { Observable, Subject, Subscription } from 'rxjs';
import { BoundsSelectionStrategyService } from './bounds-selection-strategy.service';
import { BoundsEndSelectionEventData } from '../types/bounds-end-selection-event-data.type';
import { SelectTool } from '../enums/select-tool.enum';
import { SamStateQuery } from '../stores/sam-state/sam-state-query.service';
import { takeUntil } from 'rxjs/operators';
import { SamMode } from '../enums/sam-mode.enum';
import { ShapeStylesName } from '../enums/shape-styles-name.enum';
import { Feature, Polygon } from 'geojson';

export type EndSelectionEventData =
    CircleEndSelectionEventData
    | PolygonEndSelectionEventData
    | BoundsEndSelectionEventData;

export type SelectionStrategyType = SelectionStrategy<EndSelectionEventData>;

@Injectable()
export class SelectionToolContextService {
    private readonly confirmSelectionEvent$ = new Subject<EndSelectionEventData>();
    private destroy$ = new Subject<void>();
    private map: Map;
    private endSelectionEventSubscription?: Subscription;
    private confirmSelectionEventSubscription?: Subscription;
    private strategy: SelectionStrategy<EndSelectionEventData> = null;

    constructor(
        private readonly circleSelectionStrategy: CircleSelectionStrategyService,
        private readonly polygonSelectionStrategy: PolygonSelectionStrategyService,
        private readonly boundsSelectionStrategyService: BoundsSelectionStrategyService,
        private readonly samStateQuery: SamStateQuery,
    ) {
        this.setStrategy(this.boundsSelectionStrategyService);
    }

    public isPointInsideShape(pointCoordinates: LngLat): boolean {
        return this.strategy.isPointInsideShape(pointCoordinates);
    }

    public setTool(tool: SelectTool): void {
        switch (tool) {
            case SelectTool.bounds:
                this.setStrategy(this.boundsSelectionStrategyService);
                break;
            case SelectTool.circle:
                this.setStrategy(this.circleSelectionStrategy);
                break;
            case SelectTool.polygon:
                this.setStrategy(this.polygonSelectionStrategy);
                break;
        }
    }

    public setStrategy(strategy: SelectionStrategyType): void {
        if (this.strategy && this.strategy.isActive) {
            this.strategy.deactivate();
        }

        this.strategy = strategy;

        if (this.endSelectionEventSubscription) {
            this.endSelectionEventSubscription.unsubscribe();
            this.endSelectionEventSubscription = null;
        }

        if (this.confirmSelectionEventSubscription) {
            this.confirmSelectionEventSubscription.unsubscribe();
            this.confirmSelectionEventSubscription = null;
        }

        this.confirmSelectionEventSubscription = this.strategy.confirmSelectionEvent()
            .subscribe((data) => this.confirmSelectionEvent$.next(data));
    }

    public initialize(map: Map): void {
        this.map = map;
        this.subscribeOnSamStateChanges();
    }

    public activate(): void {
        const isActive = this.strategy.isActive;
        if (!isActive) {
            this.strategy.activate(this.map);
        }
    }

    public startSelecting(): void {
        this.strategy.startSelecting();
    }

    public clear(): void {
        this.strategy.clear();
    }

    public confirmSelectionEvent(): Observable<EndSelectionEventData> {
        return this.confirmSelectionEvent$.asObservable();
    }

    public deactivate(): void {
        const isActive = this.strategy?.isActive;
        if (isActive) {
            this.strategy.deactivate();
        }
    }

    public getLoadedAreaPolygon(): Feature<Polygon> | null {
        if (!this.map) {
            return null;
        }

        return this.strategy.getLoadedAreaPolygon();
    }

    private subscribeOnSamStateChanges(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.destroy$ = new Subject<void>();
        this.samStateQuery.select('activeTool')
            .pipe(takeUntil(this.destroy$))
            .subscribe((tool) => {
                this.deactivate();

                if (tool) {
                    this.setTool(tool);
                    this.activate();
                    this.startSelecting();
                }
            });
        this.samStateQuery.select('mode')
            .pipe(takeUntil(this.destroy$))
            .subscribe((mode) => {
                switch (mode) {
                    case SamMode.displayingPolygons:
                        this.strategy.setStyle(ShapeStylesName.transparency);
                        break;
                    default:
                        this.strategy.setStyle(ShapeStylesName.default);
                }
            });
    }
}
