import { LngLat, Map } from 'maplibre-gl';
import { Observable } from 'rxjs';
import { ShapeStylesName } from '../enums/shape-styles-name.enum';
import { Feature, Polygon } from 'geojson';

export type SelectionStrategy<EndSelectionEventData> = {
    isActive: boolean;
    isPointInsideShape: (point: LngLat) => boolean;
    activate: (map: Map) => void;
    startSelecting: () => void;
    stopSelecting: () => void;
    deactivate: () => void;
    confirmSelection: () => void;
    confirmSelectionEvent: () => Observable<EndSelectionEventData>;
    clear: () => void;
    undo: () => void;
    setStyle: (styleName: ShapeStylesName) => void;
    getLoadedAreaPolygon: () => Feature<Polygon> | null;
}
