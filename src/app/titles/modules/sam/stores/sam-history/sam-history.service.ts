import { Injectable } from '@angular/core';
import { SamHistoryApi } from '../../api/sam-history.api';
import { SamHistoryStore } from './sam-history.store';
import { Subject } from 'rxjs';
import { TeamProjectsQueryParams } from '../../../../../teamwork-common/types/teamwork-query-params.type';
import { LoggerService } from '@services';
import { finalize, switchMap, takeUntil } from 'rxjs/operators';

@Injectable()
export class SamHistoryService {
    private loadHistorySubject: Subject<TeamProjectsQueryParams | undefined>;
    private destroy$: Subject<void>;


    constructor(
        private readonly api: SamHistoryApi,
        private readonly store: SamHistoryStore,
        private readonly log: LoggerService,
    ) {
    }

    public init(): void {
        this.loadHistorySubject = new Subject<TeamProjectsQueryParams | undefined>();
        this.destroy$ = new Subject<void>();
        this.setupLoadStream();
    }

    public finalize(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.loadHistorySubject.complete();
    }

    public loadHistory(queryParams?: TeamProjectsQueryParams): void {
        this.loadHistorySubject.next(queryParams);
    }

    public clear(): void {
        this.store.reset();
    }

    private setupLoadStream(): void {
        this.loadHistorySubject
            .pipe(
                takeUntil(this.destroy$),
                switchMap(() => {
                    this.store.setLoading(true);

                    return this.api.getAll()
                        .pipe(
                            finalize(() => this.store.setLoading(false)),
                        );
                }),
            )
            .subscribe({
                next: (response) => {
                    const items = response.body;
                    this.store.set(items);
                },
                error: (error) => {
                    this.log.error('SAM history loading error', error);

                    if (error.status === 404) {
                        this.store.set([]);
                        return;
                    }

                    this.store.setError('Failed to load SAM history');
                },
            });
    }
}
