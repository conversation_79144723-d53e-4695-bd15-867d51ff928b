import { Routes } from '@angular/router';
import { NotFoundPageComponent } from '@shared/components/not-found-page/not-found-page.component';
import { SelectAvailableToolResolver } from './core/resolvers/select-available-tool.resolver.service';
import { ExternalRedirectComponent } from '@shared/components/external-redirect/external-redirect.component';
import { AuthGuard } from '@auth/guards';
import { PdfViewerComponent } from '@shared/components/pdf-viewer/pdf-viewer.component';
import { NoSubscriptionsComponent } from '@shared/components/no-subscriptions/no-subscriptions.component';
import { AvailToolKey } from '@enums';
import { ToolAccessGuard } from './core/guards/tool-access.guard';

export const appRoutes: Routes = [
    {
        path: 'view',
        component: ExternalRedirectComponent,
        canActivate: [AuthGuard],
    },
    {
        path: 'lease',
        canActivate: [AuthGuard],
        children: [
            {
                path: '',
                data: { tool: AvailToolKey.lease, title: 'Avail Lease' },
                canActivate: [ToolAccessGuard],
                loadChildren: () => import('./leases/leases.module').then((m) => m.LeasesModule),
            },
        ],
    },
    {
        path: 'leases',
        redirectTo: 'lease',
    },
    {
        path: 'notice',
        canActivate: [AuthGuard],
        children: [
            {
                path: '',
                data: { tool: AvailToolKey.notice, title: 'Avail Notice' },
                canActivate: [ToolAccessGuard],
                loadChildren: () => import('./notices/notices.module').then((m) => m.NoticesModule),
            },
        ],
    },
    {
        path: 'notices',
        redirectTo: 'notice',
    },
    {
        path: 'title',
        canActivate: [AuthGuard],
        children: [
            {
                path: '',
                data: { tool: AvailToolKey.title, title: 'Avail Title' },
                canActivate: [ToolAccessGuard],
                loadChildren: () => import('./titles/titles.module').then((m) => m.TitlesModule),
            },
        ],
    },
    {
        path: 'titles',
        redirectTo: 'title',
    },
    {
        path: 'avail-policies',
        children: [
            {
                path: 'privacy-policy',
                component: PdfViewerComponent,
                data: {
                    documentUrl: 'assets/documents/Privacy%20Policy%20-%20Platform.pdf',
                },
            },
        ],
    },
    {
        path: 'zero-subscriptions',
        component: NoSubscriptionsComponent,
    },
    {
        path: '**',
        canActivate: [AuthGuard],
        resolve: [SelectAvailableToolResolver],
        component: NotFoundPageComponent,
        data: {
            title: 'Page Not Found',
            description: 'This url is undefined in our system. Try to use another one',
            isBackButtonVisible: false,
        },
    },
];
