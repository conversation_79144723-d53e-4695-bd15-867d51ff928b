/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { Observable } from 'rxjs';

@Injectable()
export class HealthApi {

    constructor(
        private readonly http: HttpClient,
    ) {
    }


    public getBackendVersion(): Observable<string> {
        return this.http.get('/api/health/version', { responseType: 'text' });
    }
}
