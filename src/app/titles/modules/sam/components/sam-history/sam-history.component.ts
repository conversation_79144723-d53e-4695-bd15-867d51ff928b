import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { SamHistoryService } from '../../stores/sam-history/sam-history.service';

@Component({
    selector: 'avl-sam-history',
    templateUrl: './sam-history.component.html',
    styleUrls: ['./sam-history.component.scss'],
})
export class SamHistoryComponent implements OnInit, OnDestroy {

    constructor(
        private readonly samHistoryService: SamHistoryService,
    ) {
    }

    public ngOnInit(): void {
        this.samHistoryService.init();
    }

    public ngOnDestroy(): void {
        this.samHistoryService.finalize();
        this.samHistoryService.clear();
    }

}
