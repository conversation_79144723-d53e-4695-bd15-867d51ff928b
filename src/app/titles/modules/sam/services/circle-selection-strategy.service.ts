import { Injectable } from '@angular/core';
import { BaseSelection } from './base-selection';
import { DotOptions, PrimitiveElementsService } from './primitive-elements.service';
import { LngLat, Map, MapMouseEvent } from 'maplibre-gl';
import { SelectionStrategy } from '../types/selection-strategy.type';
import { Observable, Subject } from 'rxjs';
import { CircleEndSelectionEventData } from '../types/circle-end-selection-event-data.type';
import * as turf from '@turf/turf';
import { SelectTool } from '../enums/select-tool.enum';
import { SamStateService } from '../stores/sam-state/sam-state.service';
import { SamMode } from '../enums/sam-mode.enum';
import { ShapeStylesName } from '../enums/shape-styles-name.enum';
import { CircleShapeStyles } from '../types/circle-shape-styles.type';
import { MapSnackbarStatus } from '../../map-search/enums/map-snackbar-status.enum';
import { MousePositionPopupService } from '../../../../maplibre/services/mouse-position-popup.service';
import { HintsService } from './hints.service';
import { SamHint } from '../enums/sam-hint.enum';
import { Feature, Polygon } from 'geojson';
import { LoggerService } from '@services';
import { ProfileService } from '@services';

@Injectable()
export class CircleSelectionStrategyService extends BaseSelection implements SelectionStrategy<CircleEndSelectionEventData> {
    public readonly defaultStyle: CircleShapeStyles = {
        fillColor: 'rgba(116, 142, 206, 0.2)',
        borderColor: 'rgba(116, 142, 206, 1)',
        drawingBorderColor: 'transparent',
        lineWidth: 1,
        dotColor: 'rgba(116, 142, 206, 1)',
        dotBackgroundColor: 'rgba(116, 142, 206, 0.3)',
        lineDash: [],
    };

    public readonly transparencyStyle: CircleShapeStyles = {
        ...this.defaultStyle,
        fillColor: 'transparent',
        borderColor: 'rgba(116, 142, 206, 0.9)',
        dotColor: 'rgba(116, 142, 206, 0.9)',
        dotBackgroundColor: 'transparent',
        lineDash: [5, 5],
    };

    private readonly confirmSelectionEvent$ = new Subject<CircleEndSelectionEventData>();
    private centerPoint?: LngLat = null;
    private cursorPoint?: LngLat = null;
    private radiusInKm = 0;
    private isDrawing = false;
    private styles = this.defaultStyle;

    constructor(
        private readonly primitives: PrimitiveElementsService,
        private readonly samStateService: SamStateService,
        private readonly hintsService: HintsService,
        private readonly mouseMapPositionPopupService: MousePositionPopupService,
        private readonly log: LoggerService,
        private readonly profile: ProfileService,
    ) {
        super();
        this.render = this.render.bind(this);
        this.resizeCanvas = this.resizeCanvas.bind(this);
        this.onMouseDown = this.onMouseDown.bind(this);
        this.onMouseMove = this.onMouseMove.bind(this);
        this.onMouseUp = this.onMouseUp.bind(this);
    }

    public isPointInsideShape(pointCoordinates: LngLat): boolean {
        if (!this.centerPoint) {
            return false;
        }

        const pointToCheck = turf.point([pointCoordinates.lng, pointCoordinates.lat]);
        const center = turf.point([this.centerPoint.lng, this.centerPoint.lat]);
        const distance = turf.distance(pointToCheck, center);

        return distance <= this.restrictedRadius();
    }

    public activate(map: Map): void {
        super.activate(map);
        this.subscribeOnMapEvents();
    }

    public startSelecting(): void {
        super.startSelecting();
        this.map.dragPan.disable();
        this.setTargetCursor();
        this.samStateService.updateMode(SamMode.selecting);
        this.mouseMapPositionPopupService.show();
    }

    public stopSelecting(): void {
        super.stopSelecting();
        this.map.dragPan.enable();
        this.setDefaultCursor();
        this.samStateService.clearMessages();
        this.mouseMapPositionPopupService.hide();
    }

    public clear(): void {
        this.samStateService.updateMode(SamMode.toolSelected);
        this.centerPoint = null;
        this.cursorPoint = null;
        this.radiusInKm = 0;
        this.isDrawing = false;
        this.clearCanvas();
        this.samStateService.markAsPure();
        this.samStateService.clearMessages();
        this.render();
    }

    public undo(): void {
        this.clear();
    }

    public confirmSelection(): void {
        this.samStateService.updateMode(SamMode.displayingPolygons);
        this.hintsService.remove(SamHint.confirmSelectedArea);
        const endEventData: CircleEndSelectionEventData = {
            type: SelectTool.circle,
            center: this.centerPoint,
            radius: this.restrictedRadius(),
        };
        this.confirmSelectionEvent$.next(endEventData);
    }

    public confirmSelectionEvent(): Observable<CircleEndSelectionEventData> {
        return this.confirmSelectionEvent$.asObservable();
    }

    public deactivate(): void {
        if (this.isActive) {
            this.stopSelecting();
            this.clear();
            this.unsubscribeFromMapEvents();
            this.samStateService.updateMode(SamMode.noToolSelected);
            super.deactivate();
        }
    }

    public setStyle(styleName: ShapeStylesName): void {
        if (styleName === ShapeStylesName.transparency) {
            this.styles = this.transparencyStyle;
        } else {
            this.styles = this.defaultStyle;
        }

        this.render();
    }

    public getLoadedAreaPolygon(): Feature<Polygon> | null {
        try {
            if (!this.centerPoint || this.radiusInKm <= 0) {
                this.log.warn('Cannot create polygon: missing center point or invalid radius');
                return null;
            }

            const centerCoords = this.centerPoint.toArray();
            const radius = this.restrictedRadius();
            const circleOptions = { steps: 64 };

            return turf.circle(centerCoords, radius, circleOptions);
        } catch (error) {
            this.log.error('Error creating circle polygon:', error);
            return null;
        }
    }

    protected render(): void {
        if (!this.context) {
            return;
        }

        this.clearCanvas();

        if (this.centerPoint) {
            const centerPoint = this.map.project([this.centerPoint.lng, this.centerPoint.lat]);
            const cursorPoint = this.map.project([this.cursorPoint.lng, this.cursorPoint.lat]);
            const radiusInPx = Math.sqrt(
                Math.pow(cursorPoint.x - centerPoint.x, 2)
                + Math.pow(cursorPoint.y - centerPoint.y, 2),
            );
            const dotStyles: DotOptions = {
                color: this.styles.dotColor,
                backgroundColor: this.styles.dotBackgroundColor,
            };

            this.primitives.renderDotWithBackground(this.context, centerPoint, dotStyles);

            if (this.isDrawing) {
                this.primitives.renderLine(this.context, centerPoint, cursorPoint, { isDash: true });
            }

            this.context.fillStyle = this.styles.fillColor;
            this.context.strokeStyle = this.isDrawing ? this.styles.drawingBorderColor : this.styles.borderColor;
            this.context.lineWidth = this.styles.lineWidth;

            this.context.beginPath();
            this.context.setLineDash(this.styles.lineDash);
            this.context.arc(centerPoint.x, centerPoint.y, radiusInPx, 0, Math.PI * 2);
            this.context.fill();
            this.context.stroke();
        }
    }

    private subscribeOnMapEvents(): void {
        this.map.on('move', this.render);
        this.map.on('resize', this.resizeCanvas);
        this.map.on('zoom', this.render);
        this.map.on('mousedown', this.onMouseDown);
        this.map.on('mousemove', this.onMouseMove);
        this.map.on('mouseup', this.onMouseUp);
    }

    private unsubscribeFromMapEvents(): void {
        this.map.off('move', this.render);
        this.map.off('resize', this.resizeCanvas);
        this.map.off('zoom', this.render);
        this.map.off('mousedown', this.onMouseDown);
        this.map.off('mousemove', this.onMouseMove);
        this.map.off('mouseup', this.onMouseUp);
    }

    private onMouseDown(event: MapMouseEvent): void {
        if (this.isDrawing) {
            this.onMouseUp();
        }

        if (this.isSelection) {
            this.centerPoint = event.lngLat;
            this.cursorPoint = event.lngLat;
            this.isDrawing = true;
        }
    }

    private onMouseUp(): void {
        if (!this.isSelection) {
            return;
        }

        this.isDrawing = false;
        this.render();

        const minRadiusKm = this.profile.samCircleMinRadiusKm$.getValue();

        if (this.radiusInKm > minRadiusKm) {
            this.samStateService.markAsDirty();
            this.stopSelecting();
            this.samStateService.updateMode(SamMode.waitingConfirmation);
            this.hintsService.add(SamHint.confirmSelectedArea);
            this.fitBoundsToShape(this.getCoordinates());
        } else {
            this.clear();
        }
    }

    private onMouseMove(event: MapMouseEvent): void {
        if (this.isDrawing && this.centerPoint) {
            const newCursorPoint = event.lngLat;
            const maxRadiusKm = this.profile.samCircleMaxRadiusKm$.getValue();
            this.radiusInKm = this.calcRadiusInKm(this.centerPoint, newCursorPoint);

            if (this.radiusInKm <= maxRadiusKm) {
                this.cursorPoint = newCursorPoint;
            } else {
                const line = turf.lineString([
                    [this.centerPoint.lng, this.centerPoint.lat],
                    [newCursorPoint.lng, newCursorPoint.lat],
                ]);
                const pointOnLine = turf.along(line, maxRadiusKm);
                this.cursorPoint = new LngLat(pointOnLine.geometry.coordinates[0], pointOnLine.geometry.coordinates[1]);
            }

            this.updateAreaInfo(this.radiusInKm * 1000);
            this.render();
        }
    }

    private calcRadiusInKm(center: LngLat, end: LngLat): number {
        const from = turf.point([center.lng, center.lat]);
        const to = turf.point([end.lng, end.lat]);

        return turf.distance(from, to);
    }

    private updateAreaInfo(radius: number): void {
        const maxRadiusKm = this.profile.samCircleMaxRadiusKm$.getValue();
        const minRadiusKm = this.profile.samCircleMinRadiusKm$.getValue();
        const isMaxRestrictionViolated = this.radiusInKm > maxRadiusKm;
        const isMinRestrictionViolated = this.radiusInKm < minRadiusKm;
        const roundedRadius = Math.round(radius);

        let restriction = '';
        if (isMinRestrictionViolated) {
            const roundedMinRadius = Math.round(minRadiusKm * 1000);
            restriction = ` (min ${roundedMinRadius}m)`;
        } else if (isMaxRestrictionViolated) {
            const roundedMaxRadius = Math.round(maxRadiusKm * 1000);
            restriction = ` (max ${roundedMaxRadius}m)`;
        }

        const message = {
            message: `Radius: ${roundedRadius} meters${restriction}`,
            status: MapSnackbarStatus.info,
        };
        this.samStateService.updateMessages(message);
    }

    private restrictedRadius(): number {
        const maxRadiusKm = this.profile.samCircleMaxRadiusKm$.getValue();

        return Math.min(this.radiusInKm, maxRadiusKm);
    }

    private getCoordinates(): number[][] {
        const circleOptions = { steps: 4 };
        const centerCoords = this.centerPoint.toArray();
        const radius = this.restrictedRadius();
        const turfCircle = turf.circle(centerCoords, radius, circleOptions);

        return turfCircle.geometry.coordinates[0] as number[][];
    }
}
