import { Injectable } from '@angular/core';
import { CanDeactivate } from '@angular/router';
import { Observable } from 'rxjs';
import { OnboardingManageService } from '../services';

export type CanComponentDeactivate = {
    canDeactivate: () => Observable<boolean> | Promise<boolean> | boolean;
}

@Injectable()
export class ConfirmExitOnboardingGuard implements CanDeactivate<CanComponentDeactivate> {

    constructor(
        private readonly onboarding: OnboardingManageService,
    ) {
    }

    public canDeactivate(): Observable<boolean> | Promise<boolean> | boolean {
        if (!this.onboarding.isActive) {
            return true;
        }

        const isConfirmed = confirm('Are you sure you want to leave the page and skip the onboarding branch?');
        if (isConfirmed) {
            this.onboarding.skipBranch();
        }

        return isConfirmed;
    }

}
