import { Injectable } from '@angular/core';
import { Query } from '@datorama/akita';
import { SamStateState, SamStateStore } from './sam-state.store';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { SamMode } from '../../enums/sam-mode.enum';
import { MapSnackbarMessage } from '../../../map-search/types/map-snackbar-message.type';
import { mapSnackbarStatusOrder } from '../../../map-search/enums/map-snackbar-status.enum';

@Injectable()
export class SamStateQuery extends Query<SamStateState> {

    constructor(protected store: SamStateStore) {
        super(store);
    }

    public selectIsDisplayingPolygonsModeEnabled(): Observable<boolean> {
        return this.select('mode')
            .pipe(
                map((mode) => mode === SamMode.displayingPolygons),
            );
    }

    public selectIsConfirmationModeEnabled(): Observable<boolean> {
        return this.select('mode')
            .pipe(
                map((mode) => mode === SamMode.waitingConfirmation),
            );
    }

    public selectIsSamDisabled(): Observable<boolean> {
        return this.select('isEnabled')
            .pipe(
                map((isEnabled) => !isEnabled),
            );
    }

    public selectIsMapInteractionAvailable(): Observable<boolean> {
        return this.select('mode')
            .pipe(
                map((mode) =>
                    mode === SamMode.noToolSelected
                    || mode === SamMode.displayingPolygons,
                ),
            );
    }

    public selectMessages(): Observable<MapSnackbarMessage[]> {
        return this.select('messages')
            .pipe(
                map((messagesObject) => Object.values(messagesObject)),
                map((messages) =>
                    messages.sort((a, b) => mapSnackbarStatusOrder[a.status] - mapSnackbarStatusOrder[b.status]),
                ),
            );
    }

    public isMapInteractionAvailable(): boolean {
        const mode = this.getValue().mode;

        return mode === SamMode.noToolSelected
            || mode === SamMode.displayingPolygons;
    }
}
