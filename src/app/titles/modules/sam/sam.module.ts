import { NgModule } from '@angular/core';
import { PrimitiveElementsService } from './services/primitive-elements.service';
import { CircleSelectionStrategyService } from './services/circle-selection-strategy.service';
import { PolygonSelectionStrategyService } from './services/polygon-selection-strategy.service';
import { SelectionToolContextService } from './services/selection-tool-context.service';
import { BoundsSelectionStrategyService } from './services/bounds-selection-strategy.service';
import { SelectControlsComponent } from './components/select-controls/select-controls.component';
import { CircleSelectControlsComponent } from './components/circle-select-controls/circle-select-controls.component';
import { SamStateStore } from './stores/sam-state/sam-state.store';
import { SamStateQuery } from './stores/sam-state/sam-state-query.service';
import { SamStateService } from './stores/sam-state/sam-state.service';
import { DisplayingPolygonsModeControlsComponent } from './components/displaying-polygons-mode-controls/displaying-polygons-mode-controls.component';
import { PolygonSelectControlsComponent } from './components/polygon-select-controls/polygon-select-controls.component';
import { HintsService } from './services/hints.service';
import { ToolSelectButtonComponent } from './components/tool-select-button/tool-select-button.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SharedModule } from '@shared/shared.module';
import { SamTourModule } from '../sam-tour/sam-tour.module';
import { SamHistoryComponent } from './components/sam-history/sam-history.component';
import { SamHistoryService } from './stores/sam-history/sam-history.service';
import { SamHistoryStore } from './stores/sam-history/sam-history.store';
import { SamHistoryQuery } from './stores/sam-history/sam-history.query';
import { SamHistoryApi } from './api/sam-history.api';


@NgModule({
    declarations: [
        SelectControlsComponent,
        CircleSelectControlsComponent,
        DisplayingPolygonsModeControlsComponent,
        PolygonSelectControlsComponent,
        ToolSelectButtonComponent,
        SamHistoryComponent,
    ],
    imports: [
        MatTooltipModule,
        SharedModule,
        SamTourModule,
    ],
    providers: [
        PrimitiveElementsService,
        CircleSelectionStrategyService,
        PolygonSelectionStrategyService,
        SelectionToolContextService,
        BoundsSelectionStrategyService,
        SamStateStore,
        SamStateQuery,
        SamStateService,
        HintsService,
        SamHistoryService,
        SamHistoryStore,
        SamHistoryQuery,
        SamHistoryApi,
    ],
    exports: [
        SelectControlsComponent,
        ToolSelectButtonComponent,
        SamHistoryComponent,
    ],
})
export class SamModule {
}
