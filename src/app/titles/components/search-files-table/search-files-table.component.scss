@import '~assets/sass/variables';
@import '~assets/sass/mixins';
@import '~assets/sass/animations';

\:host {
    overflow: hidden;
}

$row-height: 50px;

.container {
    height: 100%;
    background-color: #fff;
    overflow: auto;

    &::-webkit-scrollbar {
        width: 5px;
    }

    &::-webkit-scrollbar-track {
        background-color: $gray;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background-color: $dark-gray;
    }
}

.table {
    position: relative;
    width: 100%;
    border-spacing: 0;
}

.row {
    font-size: 16px;
    color: $black;
    background-color: white;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: #f7f7f7;
    }

    &__loading {
        &:nth-child(2n) {
            .title {
                .loading-box {
                    width: 68px;
                }
            }

            .tenure {
                .loading-box {
                    width: 80%;
                }
            }

            .address {
                .loading-box:not(.square) {
                    width: 60%;
                }
            }
        }
    }
}

.remove-last-row-border {
    &:last-of-type {
        .column {
            border-bottom: none;
        }
    }
}

.icon {
    position: absolute;
    top: -5px;
    left: 0;

    transition: all 0.2s ease;
}

.icon-download {
    width: 20px;
    height: 20px;
    color: #0c6ad9;

    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.column {
    position: relative;
    padding: 15px 16px 14px;
    border-bottom: 1px solid rgba($gray, 0.3);
    line-height: 16px;

    &__download-link {
        display: flex;
    }

    &__head {
        background-color: white;
        color: $dark-gray;
        font-size: 10px;
        line-height: 14px;
        cursor: pointer;
    }

    &__head:hover {
        background-color: #f7f7f7;
    }

    @include truncate(0px);

    &:first-of-type {
        min-width: 60px;
        padding-left: 24px;
    }

    &:last-of-type {
        padding-right: 16px;
        padding-left: 0;
    }

    &__reference {
        width: 100%;
    }

    &__size {
        min-width: 110px;
    }

    &__pages {
        min-width: 80px;
    }

    &__type {
        min-width: 180px;

        &_wide {
            min-width: 290px;
        }
    }

    &__linked-titles {
        min-width: 200px;
    }

    &__remove-button {
        text-align: end;
        min-width: 36px;
    }

    &__type,
    &__linked-titles {
        padding-top: 5px;
        padding-bottom: 4px;
    }
}

.remove-button {
    display: flex;
    width: 100%;
    justify-content: flex-end;
}

.loading-column {
    padding-top: 16px;
    padding-bottom: 16px;
}

.loading-box {
    width: 100%;
    height: 18px;

    border-radius: 5px;
    background-color: $loading-pulse-background-color;
    animation: loadingPulse 1s infinite ease-in-out;

    &.square {
        width: 16px;
    }

    &.close {
        position: absolute;
        top: 16px;
        right: 18px;
    }
}

.icon-remove {
    width: 20px;
    height: 20px;
    opacity: 0.6;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
        opacity: 1;
    }
}

.tooltip-container {
    position: relative;
}

.info-tooltip {
    position: absolute;
    left: 82px;

    width: 12px;
    height: 12px;
}

.input-select,
::ng-deep .linked-titles__select .mat-form-field {
    width: 100%;
}
